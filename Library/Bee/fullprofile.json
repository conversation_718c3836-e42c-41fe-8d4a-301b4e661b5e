{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 27554, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 27554, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 27554, "tid": 8686, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 27554, "tid": 8686, "ts": 1748542002931971, "dur": 1181, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 27554, "tid": 8686, "ts": 1748542002950558, "dur": 1473, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 27554, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 27554, "tid": 1, "ts": 1748542001510951, "dur": 33444, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 27554, "tid": 1, "ts": 1748542001544401, "dur": 406420, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 27554, "tid": 1, "ts": 1748542001950836, "dur": 120453, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 27554, "tid": 8686, "ts": 1748542002952043, "dur": 17, "ph": "X", "name": "", "args": {}}, {"pid": 27554, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001505522, "dur": 8943, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001514468, "dur": 1402824, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001519732, "dur": 8736, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001528478, "dur": 2371, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001530855, "dur": 14718, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001545579, "dur": 384, "ph": "X", "name": "ProcessMessages 8186", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001545965, "dur": 52, "ph": "X", "name": "ReadAsync 8186", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001546156, "dur": 4, "ph": "X", "name": "ProcessMessages 8140", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001546161, "dur": 185, "ph": "X", "name": "ReadAsync 8140", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001546348, "dur": 3, "ph": "X", "name": "ProcessMessages 8166", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001546353, "dur": 181, "ph": "X", "name": "ReadAsync 8166", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001546551, "dur": 1, "ph": "X", "name": "ProcessMessages 896", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001546553, "dur": 22, "ph": "X", "name": "ReadAsync 896", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001546582, "dur": 2, "ph": "X", "name": "ProcessMessages 5160", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001546585, "dur": 29, "ph": "X", "name": "ReadAsync 5160", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001546616, "dur": 174, "ph": "X", "name": "ReadAsync 1187", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001546792, "dur": 2, "ph": "X", "name": "ProcessMessages 4231", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001546795, "dur": 207, "ph": "X", "name": "ReadAsync 4231", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001547004, "dur": 1, "ph": "X", "name": "ProcessMessages 1974", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001547005, "dur": 19, "ph": "X", "name": "ReadAsync 1974", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001547025, "dur": 1, "ph": "X", "name": "ProcessMessages 2206", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001547027, "dur": 858, "ph": "X", "name": "ReadAsync 2206", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001547887, "dur": 4, "ph": "X", "name": "ProcessMessages 7160", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001547891, "dur": 41, "ph": "X", "name": "ReadAsync 7160", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001547934, "dur": 21, "ph": "X", "name": "ReadAsync 1344", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001547957, "dur": 23, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001547983, "dur": 167, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001548152, "dur": 1, "ph": "X", "name": "ProcessMessages 906", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001548154, "dur": 234, "ph": "X", "name": "ReadAsync 906", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001548389, "dur": 2, "ph": "X", "name": "ProcessMessages 4604", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001548392, "dur": 250, "ph": "X", "name": "ReadAsync 4604", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001548643, "dur": 1, "ph": "X", "name": "ProcessMessages 2391", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001548645, "dur": 53, "ph": "X", "name": "ReadAsync 2391", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001548700, "dur": 1, "ph": "X", "name": "ProcessMessages 1187", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001548701, "dur": 514, "ph": "X", "name": "ReadAsync 1187", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001549217, "dur": 3, "ph": "X", "name": "ProcessMessages 5776", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001549221, "dur": 22, "ph": "X", "name": "ReadAsync 5776", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001549244, "dur": 3, "ph": "X", "name": "ProcessMessages 6164", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001549250, "dur": 73, "ph": "X", "name": "ReadAsync 6164", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001549325, "dur": 1, "ph": "X", "name": "ProcessMessages 1912", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001549327, "dur": 79, "ph": "X", "name": "ReadAsync 1912", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001549409, "dur": 48, "ph": "X", "name": "ReadAsync 1122", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001549458, "dur": 1, "ph": "X", "name": "ProcessMessages 1286", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001549460, "dur": 291, "ph": "X", "name": "ReadAsync 1286", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001549753, "dur": 1, "ph": "X", "name": "ProcessMessages 1900", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001549755, "dur": 354, "ph": "X", "name": "ReadAsync 1900", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001550110, "dur": 3, "ph": "X", "name": "ProcessMessages 6640", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001550114, "dur": 104, "ph": "X", "name": "ReadAsync 6640", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001550219, "dur": 1, "ph": "X", "name": "ProcessMessages 2000", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001550221, "dur": 101, "ph": "X", "name": "ReadAsync 2000", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001550330, "dur": 5, "ph": "X", "name": "ProcessMessages 2094", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001550337, "dur": 113, "ph": "X", "name": "ReadAsync 2094", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001550453, "dur": 2, "ph": "X", "name": "ProcessMessages 1547", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001550458, "dur": 116, "ph": "X", "name": "ReadAsync 1547", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001550820, "dur": 1, "ph": "X", "name": "ProcessMessages 1499", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001550823, "dur": 43, "ph": "X", "name": "ReadAsync 1499", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001550867, "dur": 3, "ph": "X", "name": "ProcessMessages 6800", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001550871, "dur": 221, "ph": "X", "name": "ReadAsync 6800", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001551093, "dur": 2, "ph": "X", "name": "ProcessMessages 4572", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001551096, "dur": 41, "ph": "X", "name": "ReadAsync 4572", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001551138, "dur": 1, "ph": "X", "name": "ProcessMessages 1643", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001551147, "dur": 38, "ph": "X", "name": "ReadAsync 1643", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001551188, "dur": 185, "ph": "X", "name": "ReadAsync 1219", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001551375, "dur": 2, "ph": "X", "name": "ProcessMessages 5258", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001551378, "dur": 23, "ph": "X", "name": "ReadAsync 5258", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001551403, "dur": 48, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001551453, "dur": 1, "ph": "X", "name": "ProcessMessages 1084", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001551454, "dur": 58, "ph": "X", "name": "ReadAsync 1084", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001551513, "dur": 1, "ph": "X", "name": "ProcessMessages 1593", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001551515, "dur": 47, "ph": "X", "name": "ReadAsync 1593", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001551564, "dur": 38, "ph": "X", "name": "ReadAsync 1261", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001551604, "dur": 44, "ph": "X", "name": "ReadAsync 1248", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001551650, "dur": 37, "ph": "X", "name": "ReadAsync 1295", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001551689, "dur": 46, "ph": "X", "name": "ReadAsync 1327", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001551737, "dur": 37, "ph": "X", "name": "ReadAsync 1112", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001551776, "dur": 58, "ph": "X", "name": "ReadAsync 1201", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001551837, "dur": 117, "ph": "X", "name": "ReadAsync 1315", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001551957, "dur": 5, "ph": "X", "name": "ProcessMessages 2441", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001551964, "dur": 116, "ph": "X", "name": "ReadAsync 2441", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001552081, "dur": 2, "ph": "X", "name": "ProcessMessages 2967", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001552084, "dur": 55, "ph": "X", "name": "ReadAsync 2967", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001552140, "dur": 1, "ph": "X", "name": "ProcessMessages 1515", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001552142, "dur": 39, "ph": "X", "name": "ReadAsync 1515", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001552183, "dur": 86, "ph": "X", "name": "ReadAsync 909", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001552273, "dur": 13, "ph": "X", "name": "ProcessMessages 1372", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001552856, "dur": 49, "ph": "X", "name": "ReadAsync 1372", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001552906, "dur": 3, "ph": "X", "name": "ProcessMessages 8145", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001552910, "dur": 135, "ph": "X", "name": "ReadAsync 8145", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001553048, "dur": 2, "ph": "X", "name": "ProcessMessages 753", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001553052, "dur": 35, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001553088, "dur": 1, "ph": "X", "name": "ProcessMessages 1369", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001553090, "dur": 61, "ph": "X", "name": "ReadAsync 1369", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001553154, "dur": 2, "ph": "X", "name": "ProcessMessages 1046", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001553158, "dur": 82, "ph": "X", "name": "ReadAsync 1046", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001553242, "dur": 4, "ph": "X", "name": "ProcessMessages 1890", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001553247, "dur": 298, "ph": "X", "name": "ReadAsync 1890", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001553548, "dur": 7, "ph": "X", "name": "ProcessMessages 4825", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001553557, "dur": 60, "ph": "X", "name": "ReadAsync 4825", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001553619, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001553622, "dur": 458, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001554083, "dur": 2, "ph": "X", "name": "ProcessMessages 2260", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001554085, "dur": 55, "ph": "X", "name": "ReadAsync 2260", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001554157, "dur": 375, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001554534, "dur": 3, "ph": "X", "name": "ProcessMessages 4566", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001554537, "dur": 608, "ph": "X", "name": "ReadAsync 4566", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001555178, "dur": 75, "ph": "X", "name": "ProcessMessages 3920", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001555256, "dur": 171, "ph": "X", "name": "ReadAsync 3920", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001555429, "dur": 473, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001555905, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001555907, "dur": 57, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001555967, "dur": 231, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001556200, "dur": 188, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001556391, "dur": 526, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001556919, "dur": 1, "ph": "X", "name": "ProcessMessages 1766", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001556921, "dur": 346, "ph": "X", "name": "ReadAsync 1766", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001557269, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001557270, "dur": 585, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001557859, "dur": 336, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001558202, "dur": 4, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001558208, "dur": 163, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001558374, "dur": 91, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001558649, "dur": 67, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001558718, "dur": 47, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001558767, "dur": 360, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001559129, "dur": 1, "ph": "X", "name": "ProcessMessages 1364", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001559131, "dur": 264, "ph": "X", "name": "ReadAsync 1364", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001559398, "dur": 429, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001559828, "dur": 1, "ph": "X", "name": "ProcessMessages 1300", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001559830, "dur": 186, "ph": "X", "name": "ReadAsync 1300", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001560018, "dur": 351, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001560372, "dur": 90, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001560465, "dur": 173, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001560639, "dur": 1, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001560641, "dur": 416, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001561059, "dur": 1, "ph": "X", "name": "ProcessMessages 926", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001561060, "dur": 18, "ph": "X", "name": "ReadAsync 926", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001561080, "dur": 238, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001561320, "dur": 164, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001561486, "dur": 1, "ph": "X", "name": "ProcessMessages 2039", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001561488, "dur": 251, "ph": "X", "name": "ReadAsync 2039", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001561742, "dur": 195, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001562113, "dur": 89, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001562205, "dur": 19, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001562226, "dur": 167, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001562396, "dur": 199, "ph": "X", "name": "ReadAsync 773", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001562606, "dur": 23, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001562658, "dur": 76, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001562736, "dur": 1, "ph": "X", "name": "ProcessMessages 1815", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001562737, "dur": 71, "ph": "X", "name": "ReadAsync 1815", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001562811, "dur": 209, "ph": "X", "name": "ReadAsync 885", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001563047, "dur": 181, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001563231, "dur": 23, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001563256, "dur": 689, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001563947, "dur": 2, "ph": "X", "name": "ProcessMessages 4003", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001563950, "dur": 16, "ph": "X", "name": "ReadAsync 4003", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001563978, "dur": 186, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001564166, "dur": 326, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001564493, "dur": 1, "ph": "X", "name": "ProcessMessages 1614", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001564495, "dur": 368, "ph": "X", "name": "ReadAsync 1614", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001564864, "dur": 1, "ph": "X", "name": "ProcessMessages 1411", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001564866, "dur": 111, "ph": "X", "name": "ReadAsync 1411", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001564979, "dur": 190, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001565171, "dur": 30, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001565203, "dur": 177, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001565383, "dur": 356, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001565740, "dur": 1, "ph": "X", "name": "ProcessMessages 867", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001565742, "dur": 58, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001565802, "dur": 172, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001565977, "dur": 15, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001566003, "dur": 877, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001566882, "dur": 1, "ph": "X", "name": "ProcessMessages 936", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001566883, "dur": 30, "ph": "X", "name": "ReadAsync 936", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001568123, "dur": 1, "ph": "X", "name": "ProcessMessages 1833", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001568126, "dur": 184, "ph": "X", "name": "ReadAsync 1833", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001568312, "dur": 2, "ph": "X", "name": "ProcessMessages 5002", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001568315, "dur": 260, "ph": "X", "name": "ReadAsync 5002", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001568577, "dur": 2, "ph": "X", "name": "ProcessMessages 2546", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001568579, "dur": 156, "ph": "X", "name": "ReadAsync 2546", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001568738, "dur": 25, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001568765, "dur": 246, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001569013, "dur": 758, "ph": "X", "name": "ReadAsync 951", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001569773, "dur": 3, "ph": "X", "name": "ProcessMessages 4733", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001569776, "dur": 3180, "ph": "X", "name": "ReadAsync 4733", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001572962, "dur": 6, "ph": "X", "name": "ProcessMessages 8160", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001572969, "dur": 246, "ph": "X", "name": "ReadAsync 8160", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001573223, "dur": 16, "ph": "X", "name": "ProcessMessages 2755", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001573260, "dur": 381, "ph": "X", "name": "ReadAsync 2755", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001573643, "dur": 1, "ph": "X", "name": "ProcessMessages 1577", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001573645, "dur": 978, "ph": "X", "name": "ReadAsync 1577", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001574630, "dur": 333, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001574965, "dur": 33, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001575000, "dur": 2, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001575002, "dur": 99, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001575105, "dur": 81, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001575188, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001575638, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001575666, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001575668, "dur": 25, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001575701, "dur": 843, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001576546, "dur": 3, "ph": "X", "name": "ProcessMessages 864", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001576550, "dur": 24, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001576582, "dur": 494, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001577077, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001577078, "dur": 105, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001577186, "dur": 686, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001577873, "dur": 3, "ph": "X", "name": "ProcessMessages 1056", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001577877, "dur": 25, "ph": "X", "name": "ReadAsync 1056", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001577904, "dur": 231, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001578137, "dur": 2, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001578140, "dur": 62, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001578205, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001578256, "dur": 103, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001578360, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001578362, "dur": 34, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001578398, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001578432, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001578453, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001578494, "dur": 70, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001578567, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001578613, "dur": 17, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001578631, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001578652, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001578680, "dur": 172, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001578854, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001578978, "dur": 153, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001579132, "dur": 2, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001579135, "dur": 52, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001579189, "dur": 208, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001579399, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001579401, "dur": 1155, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001580558, "dur": 15606, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001596171, "dur": 33, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001596218, "dur": 221, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001596443, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001596495, "dur": 3764, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001600273, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001600275, "dur": 12263, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001612558, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001612562, "dur": 657, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001613222, "dur": 113, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001613632, "dur": 9625, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001624947, "dur": 1510, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001626460, "dur": 9, "ph": "X", "name": "ProcessMessages 944", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001626486, "dur": 1448, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001627943, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001627948, "dur": 49797, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001682071, "dur": 10068, "ph": "X", "name": "ProcessMessages 1736", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542001692624, "dur": 319489, "ph": "X", "name": "ReadAsync 1736", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002012129, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002012132, "dur": 180, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002012326, "dur": 546, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002012876, "dur": 5016, "ph": "X", "name": "ProcessMessages 7477", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002017928, "dur": 393, "ph": "X", "name": "ReadAsync 7477", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002018324, "dur": 245, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002018572, "dur": 201, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002018777, "dur": 10505, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002029297, "dur": 85994, "ph": "X", "name": "ProcessMessages 2800", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002115314, "dur": 33892, "ph": "X", "name": "ReadAsync 2800", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002150124, "dur": 82, "ph": "X", "name": "ProcessMessages 2784", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002150254, "dur": 412386, "ph": "X", "name": "ReadAsync 2784", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002562678, "dur": 118, "ph": "X", "name": "ProcessMessages 1556", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002562801, "dur": 1771, "ph": "X", "name": "ReadAsync 1556", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002564576, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002564579, "dur": 1614, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002566205, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002566208, "dur": 173352, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002739575, "dur": 120, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002739703, "dur": 2916, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002742630, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002742634, "dur": 94717, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002837358, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002837361, "dur": 56, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002837421, "dur": 65, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002837488, "dur": 51, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002837541, "dur": 53, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002837596, "dur": 24, "ph": "X", "name": "ProcessMessages 5339", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002837621, "dur": 4480, "ph": "X", "name": "ReadAsync 5339", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002842112, "dur": 7, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002842122, "dur": 892, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002843018, "dur": 29, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002843049, "dur": 63714, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002906769, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002906772, "dur": 235, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002907010, "dur": 29, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002907042, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002907044, "dur": 35, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002907082, "dur": 55, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002907139, "dur": 21, "ph": "X", "name": "ProcessMessages 7965", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002907160, "dur": 4061, "ph": "X", "name": "ReadAsync 7965", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002911227, "dur": 3, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002911231, "dur": 151, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002911384, "dur": 29, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002911414, "dur": 519, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002911939, "dur": 615, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748542002912557, "dur": 4467, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 27554, "tid": 8686, "ts": 1748542002952062, "dur": 8307, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 27554, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 27554, "tid": 8589934592, "ts": 1748542001495675, "dur": 581308, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 27554, "tid": 8589934592, "ts": 1748542002076987, "dur": 7, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 27554, "tid": 8589934592, "ts": 1748542002076995, "dur": 32299, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 27554, "tid": 8686, "ts": 1748542002960550, "dur": 859, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 27554, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 27554, "tid": 4294967296, "ts": 1748542001379926, "dur": 1539336, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 27554, "tid": 4294967296, "ts": 1748542001393685, "dur": 87706, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 27554, "tid": 4294967296, "ts": 1748542002919582, "dur": 7935, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 27554, "tid": 4294967296, "ts": 1748542002925234, "dur": 62, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 27554, "tid": 4294967296, "ts": 1748542002927654, "dur": 20, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 27554, "tid": 8686, "ts": 1748542002961413, "dur": 23, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748542001498000, "dur": 13814, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748542001511833, "dur": 32509, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748542001544406, "dur": 103, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748542001544509, "dur": 99, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748542001544970, "dur": 861, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_1011BD4CAF44A078.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748542001546211, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_18DC2EF7A0439C61.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748542001550709, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748542001552848, "dur": 376, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748542001553903, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748542001554047, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748542001554754, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748542001571244, "dur": 1473, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748542001572791, "dur": 191, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748542001544614, "dur": 29067, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748542001573689, "dur": 1336049, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748542002909964, "dur": 73, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748542002910051, "dur": 824, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748542001544564, "dur": 29135, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001573702, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748542001573939, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001574158, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_33EF23AB874F1C0F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748542001574321, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_33EF23AB874F1C0F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748542001574542, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001574611, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_3DB4BB1D7CF8DED1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748542001574811, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_3FD7D90864D1DB0B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748542001574973, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001575124, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7FC5D5B6D1C0F474.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748542001575213, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001575293, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_D09E55B94B6FBE69.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748542001575388, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001575454, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_D296BA1279712408.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748542001575567, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001575623, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9191DFAD59FAEA6A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748542001575741, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_54CEF74303048885.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748542001575810, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001575885, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_B5FD6E098A39A5D2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748542001576146, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_2AF15A5FC73B3288.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748542001576251, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_C20199F83674ABBE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748542001576607, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748542001576706, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748542001576980, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748542001577059, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748542001577229, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748542001577604, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748542001577955, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748542001578262, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001578354, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001578544, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001578607, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001578660, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748542001578816, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001578884, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001579010, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748542001579142, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001579287, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001579364, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748542001579536, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001580124, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001580710, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001581292, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001581840, "dur": 633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001582474, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001583195, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001583857, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001584549, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001585206, "dur": 942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001586149, "dur": 942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001587091, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001587744, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001588616, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001589486, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001590299, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001590980, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001591660, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001592378, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001593075, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001593746, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001594397, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001595068, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001595772, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001596543, "dur": 939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001597482, "dur": 820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001598303, "dur": 2030, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001600333, "dur": 2660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001602994, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001604819, "dur": 538, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Unity/LudiqBehaviour.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748542001604179, "dur": 2249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001606429, "dur": 1907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001608336, "dur": 980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001609316, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001610121, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001610871, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001611647, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001612567, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748542001613102, "dur": 5305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748542001618408, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001618813, "dur": 682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748542001619516, "dur": 6556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748542001626073, "dur": 1259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001627351, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Runtime.ref.dll_AA9CC9DBDD612E3E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748542001627416, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001627535, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748542001628077, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001628359, "dur": 5886, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748542001634246, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001634359, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748542001634503, "dur": 907, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748542001635411, "dur": 369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001635822, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748542001635929, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748542001636151, "dur": 2362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748542001638514, "dur": 3803, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001642378, "dur": 946, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542001643324, "dur": 376237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002019563, "dur": 3122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748542002022686, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002022863, "dur": 5563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748542002028427, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002028647, "dur": 1662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748542002030310, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002030450, "dur": 703, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748542002031155, "dur": 2955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748542002034111, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002034173, "dur": 8896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748542002043070, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002043188, "dur": 2884, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748542002046073, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002046580, "dur": 3330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748542002049910, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002049988, "dur": 3440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748542002053429, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002054299, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002054727, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002054840, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002054995, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002055172, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002055384, "dur": 477, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002055903, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002056159, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002056349, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002056625, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002056781, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002056971, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002057368, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002057505, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002057576, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002057670, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748542002057792, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002058037, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002058159, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002058406, "dur": 365, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002058850, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748542002058910, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002059061, "dur": 422, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002059492, "dur": 1726, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002061263, "dur": 1265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748542002062529, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002062605, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002062680, "dur": 4276, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748542002066967, "dur": 2522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002069519, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002069650, "dur": 492856, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002562721, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748542002562511, "dur": 1640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748542002564666, "dur": 241, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002835586, "dur": 440, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542002565202, "dur": 270841, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748542002840222, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748542002840213, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748542002840333, "dur": 992, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748542002841334, "dur": 68345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542001544568, "dur": 29137, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542001573707, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748542001573907, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542001574136, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DB357A1C5B5CB80B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542001574312, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DB357A1C5B5CB80B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542001574391, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_C9A2ED22AC71DAA3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542001574868, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_317A56715ABF89E0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542001574986, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542001575063, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_24BD6C6FEF5EF086.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542001575204, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542001575275, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_18DC2EF7A0439C61.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542001575393, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542001575458, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_AA0CAB1786F0F828.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542001575614, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_1CA607ABAD2FE49C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542001575669, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542001575800, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_4D1EEC228562B063.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542001575899, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542001575965, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_458C519AD2A9288F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542001576126, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542001576183, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5836CD6851EC9E19.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542001576283, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748542001576556, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542001576729, "dur": 1576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748542001578305, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542001578548, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_AA3B5DF2776E59D2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542001578609, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542001578670, "dur": 2038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542001580738, "dur": 15285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748542001596023, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542001596291, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542001596420, "dur": 3374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542001599858, "dur": 11971, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748542001611829, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542001612098, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_BD0E97F52A0DF729.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542001612153, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542001612233, "dur": 614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542001612872, "dur": 3833, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748542001616705, "dur": 319, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542001617067, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542001617299, "dur": 1106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748542001618406, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542001618650, "dur": 674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542001619374, "dur": 1105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542001620511, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542001620689, "dur": 6151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748542001626840, "dur": 598, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542001627442, "dur": 4830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748542001632273, "dur": 392, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542001632669, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542001632763, "dur": 4239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748542001637003, "dur": 496, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542001637548, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542001637666, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542001637886, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542001637945, "dur": 1137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748542001639084, "dur": 4073, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542001643182, "dur": 2204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542001645442, "dur": 1108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748542001646550, "dur": 1309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542001647923, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542001648276, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748542001648675, "dur": 378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542001649099, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542001649355, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748542001653280, "dur": 907630, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748542002562504, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748542002563789, "dur": 174182, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748542002739004, "dur": 1293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748542002740843, "dur": 215, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542002904899, "dur": 537, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542002741219, "dur": 164226, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748542002909329, "dur": 318, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1748542002909650, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001544571, "dur": 29146, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001573719, "dur": 619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5A44DDE874DD43A2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748542001574342, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5A44DDE874DD43A2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748542001574538, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_F3E4D7EAC2485D07.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748542001574617, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_DBF9BD3B0D79F91E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748542001574884, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_3A20567EF69EC9A7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748542001575009, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001575123, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2D2E9A4895907F63.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748542001575230, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001575308, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_EEFB9A6B4613348E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748542001575412, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001575522, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_A6062A75CA920C08.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748542001575647, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_282E744BB5E921C6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748542001575748, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_3EDBDDC0989E0EE4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748542001575842, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001575907, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_6ECCF42616E9E2B7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748542001576024, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_796E436A12A0D05D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748542001576115, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FB9A8EFABECD3061.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748542001576262, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001576468, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748542001576766, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748542001577070, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748542001577272, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748542001577597, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748542001577765, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748542001577949, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748542001578046, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748542001578297, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001578373, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001578432, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001578565, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001578626, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001578693, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001578834, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001579114, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001579305, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001579542, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001580431, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001581007, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001581560, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001582132, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001582815, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001583518, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001584192, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001584860, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001585583, "dur": 1002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001586585, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001587395, "dur": 673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001588068, "dur": 1009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001589078, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001589940, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001590639, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001591343, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001592027, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001592721, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001593410, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001594080, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001594716, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001595372, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001596112, "dur": 987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001597100, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001597860, "dur": 1150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001599011, "dur": 2791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001601803, "dur": 1850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001603654, "dur": 1035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001604864, "dur": 527, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Reflection/Optimization/InstanceActionInvoker_2.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748542001605816, "dur": 529, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Reflection/Optimization/Func_6.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748542001604689, "dur": 2819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001607508, "dur": 1389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001608897, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001609764, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001610521, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001611233, "dur": 806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001612040, "dur": 1086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001613126, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748542001613237, "dur": 3403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748542001616640, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001616827, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ConversionSystem.ref.dll_B533E0F362656246.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748542001616920, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748542001617050, "dur": 1538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748542001618588, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001618772, "dur": 577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748542001619378, "dur": 4403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748542001623782, "dur": 1172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001624957, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rendering.LightTransport.Runtime.ref.dll_A1CE29DB57A110D1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748542001625017, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001625163, "dur": 580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748542001625744, "dur": 1154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001626919, "dur": 4307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748542001631226, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001631332, "dur": 2639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748542001633988, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748542001634310, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001634430, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748542001634540, "dur": 1397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748542001635937, "dur": 575, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001636576, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001636689, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748542001636791, "dur": 898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748542001637690, "dur": 5489, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001643200, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748542001643328, "dur": 1944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748542001645273, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542001645432, "dur": 374075, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542002019508, "dur": 3182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748542002022691, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542002022850, "dur": 1861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748542002024711, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542002024887, "dur": 5128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748542002030016, "dur": 407, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542002030424, "dur": 272, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748542002030700, "dur": 3567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748542002034319, "dur": 7232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748542002041553, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542002041695, "dur": 2169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748542002043865, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542002044055, "dur": 3999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748542002048055, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542002048139, "dur": 2077, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748542002050217, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542002050274, "dur": 2496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748542002052818, "dur": 16974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748542002069793, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542002069860, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748542002069920, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748542002070004, "dur": 839264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542002909333, "dur": 320, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748542001544579, "dur": 29144, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001573724, "dur": 570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_BA0B4FF91CE5F8A1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542001574299, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_BA0B4FF91CE5F8A1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542001574368, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_CD898CC368A4D4E9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542001574775, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_0726B0E54E5C8C45.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542001574846, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_B651CB6D5431C551.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542001574953, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001575073, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_F4C05CFCBFAEF91C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542001575209, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001575285, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_CC9B8F296E9C6231.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542001575404, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001575490, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_A894F34BC96CE2D6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542001575635, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_13682AC6E2C4EE7B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542001575756, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_AB798D326E6716CC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542001575825, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001575892, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_7B6D90F67FD0A1D6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542001576130, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_1C9B2F49E4A7FD04.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542001576287, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001576475, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_8C003B92E4EC36BD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542001576662, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748542001577041, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748542001577268, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748542001577744, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748542001577919, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748542001578089, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748542001578338, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001578436, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001578582, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001578652, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001578704, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001578838, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001578990, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001579319, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001579551, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001580137, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001580723, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001581306, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001581849, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001582489, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001583202, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001583869, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001584568, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001585232, "dur": 950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001586182, "dur": 928, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001587110, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001587759, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001588648, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001589529, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001590315, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001590996, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001591693, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001592401, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001593101, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001593760, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001594415, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001595092, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001595797, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001596579, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001597498, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001598257, "dur": 2308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001600566, "dur": 2597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001603164, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001604826, "dur": 535, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Reflection/Optimization/StaticFunctionInvoker_2.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748542001604232, "dur": 2602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001606835, "dur": 1569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001608406, "dur": 971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001609377, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001610182, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001610923, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001611684, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001612625, "dur": 569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542001613245, "dur": 3427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748542001616672, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001616867, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542001617207, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542001617658, "dur": 680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748542001618339, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001618613, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_4A3A50F3774BA9F7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542001618720, "dur": 849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542001619586, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542001619708, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542001619880, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542001620363, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542001620744, "dur": 5510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748542001626255, "dur": 657, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001626926, "dur": 2453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748542001629380, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001629511, "dur": 1551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748542001631063, "dur": 2226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001633324, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542001633868, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748542001634219, "dur": 775, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001634997, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542001635126, "dur": 1321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748542001636447, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001636586, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542001636668, "dur": 1838, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748542001638507, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001638645, "dur": 2417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748542001641114, "dur": 2208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542001643322, "dur": 376173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542002019496, "dur": 4613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748542002024156, "dur": 2500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748542002026657, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542002026842, "dur": 7839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748542002034681, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542002034783, "dur": 2028, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748542002036812, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542002037002, "dur": 6923, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748542002043925, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542002044103, "dur": 4122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748542002048227, "dur": 502, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542002048736, "dur": 3968, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748542002052751, "dur": 22061, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748542002074873, "dur": 834808, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001544586, "dur": 29170, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001573763, "dur": 554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A97ABBD6C696F706.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748542001574328, "dur": 206, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A97ABBD6C696F706.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748542001574535, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_3BDCA2C56F5A3F82.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748542001574710, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C72CD647BFAB69C4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748542001574896, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001575042, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_173ADBFCCFB403B8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748542001575239, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_D51B60744B48FB82.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748542001575357, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001575425, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_2793B80490192449.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748542001575598, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_2A46121DF039C105.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748542001575713, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_B2F5068140D067F0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748542001575781, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001575849, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_D95259C03EE6A7C4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748542001575944, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_242B47D8D2C44484.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748542001576172, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_DAACB4F719294F92.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748542001576260, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_42EA6A51FFD2E09A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748542001576447, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748542001576703, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748542001577006, "dur": 1018, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748542001578025, "dur": 485, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001578589, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001578649, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001578713, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001578792, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001578848, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001578996, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001579279, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001579342, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001579557, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001580146, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001580734, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001581315, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001581861, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001582405, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001583113, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001583799, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001584497, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001585154, "dur": 888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001586042, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001587034, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001587688, "dur": 846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001588534, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001589439, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001590243, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001590935, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001591613, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001592322, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001593011, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001593687, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001594340, "dur": 673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001595013, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001595710, "dur": 764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001596537, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001596623, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001597510, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001598281, "dur": 2055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001600336, "dur": 2691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001603027, "dur": 1145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001604172, "dur": 2243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001606416, "dur": 1910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001608326, "dur": 1002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001609329, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001610128, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001610860, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001611631, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001612580, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748542001613054, "dur": 562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748542001613616, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001613918, "dur": 1917, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748542001615874, "dur": 1353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748542001617227, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001617387, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748542001617498, "dur": 562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748542001618060, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001618359, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748542001618441, "dur": 425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748542001618894, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748542001619071, "dur": 12308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748542001631379, "dur": 756, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001632177, "dur": 1632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748542001633824, "dur": 1082, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748542001634906, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001634985, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.ref.dll_95D2DEE1BA1172D7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748542001635161, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748542001635403, "dur": 3915, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748542001639319, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001639545, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001639618, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748542001640105, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001640535, "dur": 89, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001640768, "dur": 53, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001641002, "dur": 2309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542001643311, "dur": 2223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748542001645551, "dur": 1086, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748542001646655, "dur": 373818, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542002020473, "dur": 5951, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748542002026475, "dur": 4635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748542002031111, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542002031199, "dur": 1831, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnity.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748542002033065, "dur": 4231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748542002037297, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542002037423, "dur": 1745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748542002039168, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542002039421, "dur": 3396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748542002042818, "dur": 414, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542002043245, "dur": 3630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748542002046911, "dur": 4543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748542002051455, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542002051534, "dur": 5956, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748542002057491, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542002057719, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542002057862, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542002057948, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/FMODUnityResonance.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748542002058005, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542002058153, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542002058242, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748542002058341, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542002058549, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542002058836, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542002058981, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748542002059091, "dur": 3436, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542002062570, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542002062654, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748542002062787, "dur": 4477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542002067265, "dur": 2127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748542002069393, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542002069502, "dur": 39530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542002109908, "dur": 5102, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 5, "ts": 1748542002115011, "dur": 10108, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 5, "ts": 1748542002125119, "dur": 3808, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 5, "ts": 1748542002109033, "dur": 19898, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542002128931, "dur": 780782, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001544594, "dur": 29175, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001573772, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_576EED2B1C54EF9B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748542001574330, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_576EED2B1C54EF9B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748542001574500, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_BB7D6B77AADC3636.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748542001574598, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_94E7D34F0A24E8EE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748542001574684, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_9361338275291BF9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748542001574890, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_73BBD5453873209A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748542001575146, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001575216, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_1D189CF611149EDC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748542001575325, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001575404, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F44CFBD4B87405AF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748542001575585, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_EE8FDF61FD2953D9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748542001575708, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_23B09F05B98149F7.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748542001575827, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_10C02A94B188B9C3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748542001575913, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001576004, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_0DC95BA72EA6C3C0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748542001576095, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_42DE6F6D94492967.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748542001576152, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001576276, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748542001576458, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001576517, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748542001577038, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001577095, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748542001577352, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748542001577848, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748542001577983, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748542001578229, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748542001578358, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001578552, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001578614, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748542001578771, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001578827, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001579158, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001579300, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001579577, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001580162, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001580770, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001581345, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001581893, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001582537, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001583249, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001583924, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001584609, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001585263, "dur": 970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001586233, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001587142, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001587808, "dur": 911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001588719, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001589650, "dur": 739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001590390, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001591086, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001591787, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001592470, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001593173, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001593842, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001594486, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001595167, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001595890, "dur": 963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001596856, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001597634, "dur": 777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001598411, "dur": 2646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001601058, "dur": 2229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001603287, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001604828, "dur": 540, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Reflection/Optimization/InvokerBase.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748542001604337, "dur": 2711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001607049, "dur": 1478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001608527, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001609425, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001610223, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001610981, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001611732, "dur": 1013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001612746, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748542001613259, "dur": 1411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748542001614671, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001614815, "dur": 1654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748542001616514, "dur": 17128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748542001633642, "dur": 681, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001634326, "dur": 1250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748542001635577, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001635714, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748542001636061, "dur": 7095, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748542001643156, "dur": 2932, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001646170, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748542001646365, "dur": 1060, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748542001647426, "dur": 516, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001647974, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001648035, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Editor.ref.dll_7BE565C70AE586E5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748542001648172, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748542001648281, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748542001648776, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542001649078, "dur": 371461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542002020539, "dur": 4582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748542002025121, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542002025186, "dur": 2380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748542002027567, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542002027794, "dur": 4631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748542002032426, "dur": 686, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542002033118, "dur": 2954, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748542002036124, "dur": 4083, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748542002040207, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542002040305, "dur": 8607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748542002048914, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542002048979, "dur": 4249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748542002053229, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542002053295, "dur": 12353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748542002065650, "dur": 1559, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542002067273, "dur": 9128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542002076402, "dur": 833394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001544601, "dur": 29178, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001573781, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B0CD21B816834329.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748542001574349, "dur": 221, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B0CD21B816834329.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748542001574570, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FED4EADA5A496243.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748542001574688, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_00177D20A4B9F3ED.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748542001574766, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_00177D20A4B9F3ED.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748542001574842, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_EE2B3985795E2BC5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748542001574904, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001575004, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_FD466DE9C9D83358.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748542001575182, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001575265, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_5D1BAF9C8C0FFD1F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748542001575375, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001575436, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0BF81E52D42208B4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748542001575604, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_110AF0D569B2AA65.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748542001575719, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_71635C60905BE81B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748542001575833, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_9927031187632C7E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748542001575913, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001575997, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D4B8FF3D259E96C1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748542001576106, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_57DA0DA15F72E273.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748542001576295, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748542001576704, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748542001577012, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748542001577244, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748542001577672, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748542001577795, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748542001578159, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001578307, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001578401, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001578557, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001578631, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001578708, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001578798, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001578855, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001579092, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001579294, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001579368, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001579569, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001580167, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001580768, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001581336, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001581883, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001582530, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001583244, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001583920, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001584605, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001585258, "dur": 966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001586224, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001587144, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001587804, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001588712, "dur": 902, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001589614, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001590368, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001591065, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001591767, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001592460, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001593162, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001593819, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001594467, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001595145, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001595862, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001596766, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001597558, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001598319, "dur": 2340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001600660, "dur": 2436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001603097, "dur": 1090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001604834, "dur": 541, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Serialization/Converters/RayConverter.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748542001604188, "dur": 2230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001606418, "dur": 1925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001608344, "dur": 1000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001609344, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001610138, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001610867, "dur": 777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001611645, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001612564, "dur": 622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748542001613209, "dur": 3498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748542001616707, "dur": 435, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001617176, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748542001617326, "dur": 1017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748542001618344, "dur": 375, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001618761, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748542001619245, "dur": 4972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748542001624217, "dur": 379, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001624618, "dur": 1418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 7, "ts": 1748542001626036, "dur": 663, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001626792, "dur": 1434, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001849439, "dur": 168937, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542001628456, "dur": 389932, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 7, "ts": 1748542002019494, "dur": 2204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748542002021699, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542002021924, "dur": 4953, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748542002026877, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542002027028, "dur": 2860, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748542002029889, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542002030022, "dur": 4511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonance.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748542002034534, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542002034612, "dur": 4510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748542002039123, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542002039454, "dur": 2525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonanceEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748542002041980, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542002042069, "dur": 4752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748542002046822, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542002046991, "dur": 2922, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748542002049913, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542002050233, "dur": 12272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748542002062506, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542002062618, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542002062710, "dur": 4489, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748542002067204, "dur": 2724, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542002069971, "dur": 770267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542002840248, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748542002840332, "dur": 1008, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748542002841341, "dur": 68507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001544609, "dur": 29177, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001573786, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_9CEC3B310263C3AF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542001574325, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_9CEC3B310263C3AF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542001574476, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_86EBD7C4DEEB8CFD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542001574530, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_542089FB00C06F3A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542001574744, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_48780D15ACA6139C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542001574854, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001574912, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_A5396449E1C706BB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542001575047, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001575154, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_B6303FFB36F7BFF8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542001575276, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001575353, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CF90820960D75094.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542001575438, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001575558, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_C90DAD4D0E34B513.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542001575666, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_FF6DF2438A8881EC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542001575790, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_50D38B6F9A6D24D8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542001575885, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001575971, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8D74880C622CE1AD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542001576176, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_90196DBEEEA0E95D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542001576270, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001576451, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001576558, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001576624, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1748542001576891, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748542001577110, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748542001577467, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748542001577657, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748542001578011, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748542001578254, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001578344, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748542001578602, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001578657, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001578727, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001578807, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001578865, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001579138, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748542001579319, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001579547, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001580128, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001580714, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001581298, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001581844, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001582482, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001583199, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001583863, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001584556, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001585214, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001586154, "dur": 945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001587099, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001587747, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001588624, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001589533, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001590319, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001591003, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001591697, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001592394, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001593094, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001593757, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001594411, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001595087, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001595792, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001596617, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001597513, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001598308, "dur": 1952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001600261, "dur": 2672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001602933, "dur": 1208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001604822, "dur": 537, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/Variables/VariableDeclarationsCloner.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748542001604141, "dur": 2090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001606232, "dur": 2048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001608281, "dur": 1020, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001609301, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001610109, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001610848, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001611598, "dur": 972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001612571, "dur": 789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542001613414, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748542001614006, "dur": 461, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001614521, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542001614966, "dur": 2497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748542001617463, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001617710, "dur": 1386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748542001619096, "dur": 612, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001619711, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542001620035, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542001620591, "dur": 3740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748542001624333, "dur": 332, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001624671, "dur": 3047, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748542001627720, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001627968, "dur": 914, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542001628918, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542001629084, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001629179, "dur": 4521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748542001633700, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001633794, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748542001634184, "dur": 460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748542001634644, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001634869, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542001635389, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542001635500, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542001635758, "dur": 1403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748542001637162, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001637295, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542001637459, "dur": 1095, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748542001638554, "dur": 867, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001639429, "dur": 1079, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748542001640544, "dur": 478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748542001641048, "dur": 2266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001643314, "dur": 5790, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542001649109, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542001649337, "dur": 372531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542002021869, "dur": 6216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748542002028086, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542002028170, "dur": 4020, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748542002032191, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542002032257, "dur": 4289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748542002036547, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542002036835, "dur": 5505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748542002042341, "dur": 924, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542002043275, "dur": 9582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748542002052902, "dur": 16114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748542002069017, "dur": 457, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542002069525, "dur": 63193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542002132720, "dur": 777105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748542002912793, "dur": 564, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 27554, "tid": 8686, "ts": 1748542002964888, "dur": 49540, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 27554, "tid": 8686, "ts": 1748542003014576, "dur": 4231, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 27554, "tid": 8686, "ts": 1748542002938169, "dur": 87538, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}